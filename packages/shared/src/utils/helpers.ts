import crypto from "crypto";

export function generateHash(obj: Record<string, any>): string {
  const str = JSON.stringify(obj);
  return crypto.createHash("sha256").update(str).digest("hex");
}

export function formatCurrencyPair(pair: string): string {
  return pair.replace(/_/g, "").toLowerCase();
}

export function flattenObject(obj: any, prefix = ""): Record<string, string> {
  const flattened: Record<string, string> = {};
  
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const newKey = prefix ? `${prefix}.${key}` : key;
      
      if (typeof obj[key] === "object" && obj[key] !== null && !Array.isArray(obj[key])) {
        Object.assign(flattened, flattenObject(obj[key], newKey));
      } else {
        flattened[newKey] = String(obj[key]);
      }
    }
  }
  
  return flattened;
}

export function unflattenObject(obj: Record<string, string>): any {
  const result: any = {};
  
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const keys = key.split(".");
      let current = result;
      
      for (let i = 0; i < keys.length - 1; i++) {
        if (!current[keys[i]]) {
          current[keys[i]] = {};
        }
        current = current[keys[i]];
      }
      
      current[keys[keys.length - 1]] = obj[key];
    }
  }
  
  return result;
}

export function isValidCurrencyPair(pair: string): boolean {
  return /^[a-zA-Z]{3,}_[a-zA-Z]{3,}$/.test(pair);
}

export function calculatePriceChange(currentPrice: number, previousPrice: number): {
  change: number;
  changePercent: number;
  status: "gainer" | "loser" | "no change";
} {
  const change = currentPrice - previousPrice;
  const changePercent = previousPrice !== 0 ? (change / previousPrice) * 100 : 0;
  
  let status: "gainer" | "loser" | "no change";
  if (change > 0) {
    status = "gainer";
  } else if (change < 0) {
    status = "loser";
  } else {
    status = "no change";
  }
  
  return { change, changePercent, status };
}
