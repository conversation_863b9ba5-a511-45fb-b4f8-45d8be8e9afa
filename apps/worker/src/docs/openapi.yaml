openapi: 3.1.0
info:
  title: Market Data REST API
  description: |
    Comprehensive REST API for retrieving historical cryptocurrency market data from MongoDB time series database.
    
    This API provides access to:
    - Real-time and historical market data
    - Aggregated OHLC statistics
    - Market statistics and analytics
    - Currency pair information
    
    **Base URL:** `http://localhost:3000/api/market-data`
    
    **Data Format:** All timestamps are in ISO 8601 format (UTC)
    
    **Rate Limiting:** Currently no rate limiting is implemented
    
    **Authentication:** No authentication required (public API)
  version: 1.0.0

servers:
  - url: http://localhost:3000/api/market-data
    description: Development server
  # - url: https://api.crypto-watcher.com/api/market-data
  #   description: Production server (example)

tags:
  - name: Health
    description: Service health and status endpoints
  - name: Market Data
    description: Market data retrieval endpoints
  - name: Statistics
    description: Market statistics and analytics
  - name: Metadata
    description: API metadata and configuration

paths:
  /health:
    get:
      tags:
        - Health
      summary: Get service health status
      description: |
        Returns comprehensive health information about the Market Data API service including:
        - Service status and uptime
        - Database connectivity (MongoDB, Redis)
        - Service version information
        
        Use this endpoint to monitor service availability and troubleshoot connectivity issues.
      operationId: getHealthCheck
      responses:
        '200':
          description: Service is healthy
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthCheckResponse'
              example:
                success: true
                data:
                  status: "healthy"
                  service: "market-data-api"
                  timestamp: "2024-01-01T00:00:00.000Z"
                  uptime: 3600
                  database:
                    mongodb: "connected"
                    redis: "connected"
                  version: "1.0.0"
                timestamp: "2024-01-01T00:00:00.000Z"
        '503':
          description: Service is unhealthy
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthCheckResponse'
              example:
                success: false
                data:
                  status: "unhealthy"
                  service: "market-data-api"
                  timestamp: "2024-01-01T00:00:00.000Z"
                  uptime: 3600
                  database:
                    mongodb: "disconnected"
                    redis: "connected"
                  version: "1.0.0"
                timestamp: "2024-01-01T00:00:00.000Z"

  /pairs:
    get:
      tags:
        - Metadata
      summary: Get available currency pairs
      description: |
        Returns a list of all available cryptocurrency trading pairs in the database.
        
        Currency pairs are returned in lowercase format (e.g., "btcusdt", "ethusdt").
        Use these exact values when querying other endpoints.
      operationId: getCurrencyPairs
      responses:
        '200':
          description: Successfully retrieved currency pairs
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CurrencyPairsResponse'
              example:
                success: true
                data:
                  pairs: ["btcusdt", "ethusdt", "adausdt", "bnbusdt"]
                  count: 4
                  lastUpdated: "2024-01-01T00:00:00.000Z"
                message: "Currency pairs retrieved successfully"
                timestamp: "2024-01-01T00:00:00.000Z"
        '500':
          $ref: '#/components/responses/InternalServerError'

  /latest/{currencyPair}:
    get:
      tags:
        - Market Data
      summary: Get latest market data
      description: |
        Returns the most recent market data point for a specific cryptocurrency trading pair.
        
        This endpoint provides real-time market information including:
        - Current price and volume
        - 24-hour price change
        - Bid/ask spreads
        - Market status (gainer/loser/no change)
      operationId: getLatestData
      parameters:
        - name: currencyPair
          in: path
          required: true
          description: |
            Currency trading pair identifier.
            
            **Supported formats:**
            - Lowercase without separator: `btcusdt`, `ethusdt`
            
            **Examples:** `btcusdt`, `ethusdt`
          schema:
            type: string
            pattern: '^([A-Z]{2,10}[/_][A-Z]{2,10}|[a-z]{6,20})$'
            example: "btcusdt"
          examples:
            bitcoin:
              value: "btcusdt"
              summary: "Bitcoin to USDT"
            ethereum:
              value: "ethusdt"
              summary: "Ethereum to USDT"
      responses:
        '200':
          description: Successfully retrieved latest market data
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LatestDataResponse'
              example:
                success: true
                data:
                  currencyPair: "btcusdt"
                  data:
                    currencyPair: "btcusdt"
                    lastPrice: "45000.00"
                    baseVolume: "1234.56"
                    timestamp: "2024-01-01T00:00:00.000Z"
                    buy: "44950.00"
                    sell: "45050.00"
                    high: "46000.00"
                    low: "44000.00"
                    status: "gainer"
                  timestamp: "2024-01-01T00:00:00.000Z"
                message: "Latest data retrieved successfully"
                timestamp: "2024-01-01T00:00:00.000Z"
        '400':
          $ref: '#/components/responses/ValidationError'
        '404':
          description: Currency pair not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                success: false
                error: "No data found for currency pair: invalidpair"
                message: "Latest data not available"
                timestamp: "2024-01-01T00:00:00.000Z"
        '500':
          $ref: '#/components/responses/InternalServerError'

  /historical:
    get:
      tags:
        - Market Data
      summary: Get historical market data
      description: |
        Returns historical market data for a specific currency pair within a date range.

        This endpoint allows you to retrieve time-series market data with:
        - Flexible date range filtering
        - Pagination support (limit parameter)
        - Chronological ordering (oldest first)

        **Use cases:**
        - Historical price analysis
        - Backtesting trading strategies
        - Data export for external analysis

        **Performance notes:**
        - Maximum date range: 1 year
        - Maximum limit: 10,000 records
        - Results are sorted by timestamp (ascending)
      operationId: getHistoricalData
      parameters:
        - name: currencyPair
          in: query
          required: true
          description: Currency trading pair identifier
          schema:
            type: string
            pattern: '^([A-Z]{2,10}[/_][A-Z]{2,10}|[a-z]{6,20})$'
            example: "btcusdt"
        - name: start
          in: query
          required: true
          description: |
            Start date for the historical data range (inclusive).

            **Format:** ISO 8601 date string
            **Examples:**
            - `2024-01-01T00:00:00.000Z`
            - `2024-01-01`
          schema:
            type: string
            format: date-time
            example: "2024-01-01T00:00:00.000Z"
        - name: end
          in: query
          required: true
          description: |
            End date for the historical data range (inclusive).

            **Format:** ISO 8601 date string
            **Note:** Must be after start date, maximum 1 year range
          schema:
            type: string
            format: date-time
            example: "2024-01-02T00:00:00.000Z"
        - name: limit
          in: query
          required: false
          description: |
            Maximum number of records to return.

            **Range:** 1-10,000
            **Default:** No limit (returns all matching records)
          schema:
            type: integer
            minimum: 1
            maximum: 10000
            example: 100
      responses:
        '200':
          description: Successfully retrieved historical data
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HistoricalDataResponse'
              example:
                success: true
                data:
                  currencyPair: "btcusdt"
                  data:
                    - currencyPair: "btcusdt"
                      lastPrice: "45000.00"
                      baseVolume: "1234.56"
                      timestamp: "2024-01-01T00:00:00.000Z"
                      buy: "44950.00"
                      sell: "45050.00"
                  count: 1
                  dateRange:
                    start: "2024-01-01T00:00:00.000Z"
                    end: "2024-01-02T00:00:00.000Z"
                message: "Historical data retrieved successfully"
                timestamp: "2024-01-01T00:00:00.000Z"
                count: 1
        '400':
          $ref: '#/components/responses/ValidationError'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /aggregated:
    get:
      tags:
        - Market Data
      summary: Get aggregated market data
      description: |
        Returns aggregated market data with OHLC-like statistics for specified time intervals.

        This endpoint provides statistical aggregations including:
        - Average, minimum, and maximum prices
        - Total trading volume
        - First and last prices (OHLC-style)
        - Record count per period

        **Supported intervals:**
        - `minute` - 1-minute intervals
        - `hour` - 1-hour intervals
        - `day` - Daily intervals
        - `week` - Weekly intervals
        - `month` - Monthly intervals

        **Use cases:**
        - Creating candlestick charts
        - Statistical analysis
        - Performance monitoring
        - Data visualization
      operationId: getAggregatedData
      parameters:
        - name: currencyPair
          in: query
          required: true
          description: Currency trading pair identifier
          schema:
            type: string
            pattern: '^([A-Z]{2,10}[/_][A-Z]{2,10}|[a-z]{6,20})$'
            example: "btcusdt"
        - name: start
          in: query
          required: true
          description: Start date for aggregation period
          schema:
            type: string
            format: date-time
            example: "2024-01-01T00:00:00.000Z"
        - name: end
          in: query
          required: true
          description: End date for aggregation period
          schema:
            type: string
            format: date-time
            example: "2024-01-02T00:00:00.000Z"
        - name: interval
          in: query
          required: true
          description: |
            Time interval for data aggregation.

            **Available intervals:**
            - `minute` - Aggregate by minute
            - `hour` - Aggregate by hour
            - `day` - Aggregate by day
            - `week` - Aggregate by week
            - `month` - Aggregate by month
          schema:
            type: string
            enum: [minute, hour, day, week, month]
            example: "hour"
      responses:
        '200':
          description: Successfully retrieved aggregated data
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AggregatedDataResponse'
              example:
                success: true
                data:
                  currencyPair: "btcusdt"
                  interval: "hour"
                  data:
                    - period: "2024-01-01 00:00:00"
                      averagePrice: 45000.00
                      minPrice: 44500.00
                      maxPrice: 45500.00
                      totalVolume: 12345.67
                      count: 60
                      firstPrice: 44800.00
                      lastPrice: 45200.00
                  count: 1
                  dateRange:
                    start: "2024-01-01T00:00:00.000Z"
                    end: "2024-01-02T00:00:00.000Z"
                message: "Aggregated data retrieved successfully"
                timestamp: "2024-01-01T00:00:00.000Z"
                count: 1
        '400':
          $ref: '#/components/responses/ValidationError'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /range:
    get:
      tags:
        - Market Data
      summary: Get market data by date range
      description: |
        Returns market data within a date range with flexible filtering options.

        This endpoint provides the most flexible data retrieval with:
        - Optional currency pair filtering (returns all pairs if not specified)
        - Configurable sorting (ascending/descending by timestamp)
        - Pagination support
        - Cross-pair analysis capabilities

        **Use cases:**
        - Multi-pair market analysis
        - Custom data exports
        - Flexible reporting
        - Cross-market comparisons
      operationId: getDataByRange
      parameters:
        - name: start
          in: query
          required: true
          description: Start date for the data range
          schema:
            type: string
            format: date-time
            example: "2024-01-01T00:00:00.000Z"
        - name: end
          in: query
          required: true
          description: End date for the data range
          schema:
            type: string
            format: date-time
            example: "2024-01-02T00:00:00.000Z"
        - name: currencyPair
          in: query
          required: false
          description: |
            Optional currency pair filter.
            If not provided, returns data for all currency pairs.
          schema:
            type: string
            pattern: '^([A-Z]{2,10}[/_][A-Z]{2,10}|[a-z]{6,20})$'
            example: "btcusdt"
        - name: limit
          in: query
          required: false
          description: Maximum number of records to return
          schema:
            type: integer
            minimum: 1
            maximum: 10000
            example: 100
        - name: sortOrder
          in: query
          required: false
          description: |
            Sort order for results by timestamp.

            **Options:**
            - `asc` - Oldest first (default)
            - `desc` - Newest first
          schema:
            type: string
            enum: [asc, desc]
            default: asc
            example: "desc"
      responses:
        '200':
          description: Successfully retrieved range data
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RangeDataResponse'
        '400':
          $ref: '#/components/responses/ValidationError'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /statistics/{currencyPair}:
    get:
      tags:
        - Statistics
      summary: Get market statistics
      description: |
        Returns comprehensive market statistics for a currency pair over a specified period.

        This endpoint provides detailed analytics including:
        - Price statistics (current, min, max, average, change)
        - Volume statistics (total, average, min, max)
        - Performance metrics (price change percentage)
        - Data coverage information

        **Supported periods:**
        - `24h` - Last 24 hours (default)
        - `7d` - Last 7 days
        - `30d` - Last 30 days

        **Use cases:**
        - Market performance analysis
        - Trading dashboard metrics
        - Portfolio tracking
        - Market comparison
      operationId: getMarketStatistics
      parameters:
        - name: currencyPair
          in: path
          required: true
          description: Currency trading pair identifier
          schema:
            type: string
            pattern: '^([A-Z]{2,10}[/_][A-Z]{2,10}|[a-z]{6,20})$'
            example: "btcusdt"
        - name: period
          in: query
          required: false
          description: |
            Time period for statistics calculation.

            **Available periods:**
            - `24h` - Last 24 hours (default)
            - `7d` - Last 7 days
            - `30d` - Last 30 days
          schema:
            type: string
            enum: [24h, 7d, 30d]
            default: "24h"
            example: "24h"
      responses:
        '200':
          description: Successfully retrieved market statistics
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MarketStatisticsResponse'
              example:
                success: true
                data:
                  currencyPair: "btcusdt"
                  period: "24h"
                  totalRecords: 1440
                  priceStats:
                    current: 45000.00
                    min: 44000.00
                    max: 46000.00
                    average: 45000.00
                    change: 500.00
                    changePercent: 1.12
                  volumeStats:
                    total: 123456.78
                    average: 85.73
                    min: 10.00
                    max: 500.00
                  dateRange:
                    start: "2024-01-01T00:00:00.000Z"
                    end: "2024-01-02T00:00:00.000Z"
                message: "Market statistics retrieved successfully"
                timestamp: "2024-01-01T00:00:00.000Z"
        '400':
          $ref: '#/components/responses/ValidationError'
        '404':
          description: No statistics available for the specified period
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                success: false
                error: "No statistics available for btcusdt in the 24h period"
                message: "Resource not found"
                timestamp: "2024-01-01T00:00:00.000Z"
        '500':
          $ref: '#/components/responses/InternalServerError'

components:
  schemas:
    # Base Response Schema
    ApiResponse:
      type: object
      properties:
        success:
          type: boolean
          description: Indicates if the request was successful
        message:
          type: string
          description: Human-readable message describing the result
        timestamp:
          type: string
          format: date-time
          description: ISO 8601 timestamp of the response
        count:
          type: integer
          description: Number of records returned (for array responses)
          minimum: 0
      required:
        - success
        - timestamp

    # Error Response Schema
    ErrorResponse:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            success:
              type: boolean
              enum: [false]
            error:
              type: string
              description: Error type or category
            details:
              type: array
              description: Detailed validation errors (optional)
              items:
                $ref: '#/components/schemas/ValidationError'
          required:
            - error

    # Validation Error Schema
    ValidationError:
      type: object
      properties:
        field:
          type: string
          description: Field name that failed validation
        message:
          type: string
          description: Validation error message
        value:
          description: The invalid value that was provided
      required:
        - field
        - message

    # Market Data Schemas
    CombinedMarketData:
      type: object
      description: Complete market data for a cryptocurrency trading pair
      properties:
        _id:
          type: string
          description: MongoDB document ID
        currencyPair:
          type: string
          description: Trading pair identifier
          example: "btcusdt"
        timestamp:
          type: string
          format: date-time
          description: Data timestamp
        buy:
          type: string
          description: Buy price
          example: "45000.00"
        sell:
          type: string
          description: Sell price
          example: "45050.00"
        low:
          type: string
          description: 24-hour low price
          example: "44000.00"
        high:
          type: string
          description: 24-hour high price
          example: "46000.00"
        open:
          type: string
          description: Opening price
          example: "44500.00"
        last:
          type: string
          description: Last traded price
          example: "45000.00"
        vol:
          type: string
          description: Trading volume
          example: "1234.56"
        lastPrice:
          type: string
          description: Last price
          example: "45000.00"
        lowestAsk:
          type: string
          description: Lowest ask price
          example: "45010.00"
        highestBid:
          type: string
          description: Highest bid price
          example: "44990.00"
        baseVolume:
          type: string
          description: Base currency volume
          example: "1234.56"
        quoteVolume:
          type: string
          description: Quote currency volume
          example: "55555555.00"
        priceChangePercent24h:
          type: string
          description: 24-hour price change percentage
          example: "+1.25"
        highestPrice24h:
          type: string
          description: Highest price in 24 hours
          example: "46000.00"
        lowestPrice24h:
          type: string
          description: Lowest price in 24 hours
          example: "44000.00"
        status:
          type: string
          enum: [gainer, loser, "no change"]
          description: Price movement status
      required:
        - currencyPair
        - timestamp

    # Aggregated Market Data Schema
    AggregatedMarketData:
      type: object
      description: Aggregated market statistics for a time period
      properties:
        period:
          type: string
          description: Time period identifier
          example: "2024-01-01 00:00:00"
        averagePrice:
          type: number
          description: Average price during the period
          example: 45000.00
        minPrice:
          type: number
          description: Minimum price during the period
          example: 44500.00
        maxPrice:
          type: number
          description: Maximum price during the period
          example: 45500.00
        totalVolume:
          type: number
          description: Total trading volume
          example: 12345.67
        count:
          type: integer
          description: Number of data points in the period
          example: 60
        firstPrice:
          type: number
          description: First price in the period (open)
          example: 44800.00
        lastPrice:
          type: number
          description: Last price in the period (close)
          example: 45200.00
      required:
        - period
        - averagePrice
        - minPrice
        - maxPrice
        - totalVolume
        - count
        - firstPrice
        - lastPrice

    # Response Schemas
    HealthCheckResponse:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                status:
                  type: string
                  enum: [healthy, unhealthy]
                  description: Overall service health status
                service:
                  type: string
                  description: Service name
                  example: "market-data-api"
                uptime:
                  type: number
                  description: Service uptime in seconds
                  example: 3600
                database:
                  type: object
                  properties:
                    mongodb:
                      type: string
                      enum: [connected, disconnected]
                      description: MongoDB connection status
                    redis:
                      type: string
                      enum: [connected, disconnected]
                      description: Redis connection status
                  required:
                    - mongodb
                    - redis
                version:
                  type: string
                  description: API version
                  example: "1.0.0"
              required:
                - status
                - service
                - uptime
                - database
                - version

    CurrencyPairsResponse:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                pairs:
                  type: array
                  items:
                    type: string
                  description: List of available currency pairs
                  example: ["btcusdt", "ethusdt", "adausdt"]
                count:
                  type: integer
                  description: Number of available pairs
                  example: 3
                lastUpdated:
                  type: string
                  format: date-time
                  description: When the pairs list was last updated
              required:
                - pairs
                - count
                - lastUpdated

    LatestDataResponse:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                currencyPair:
                  type: string
                  description: Requested currency pair
                data:
                  oneOf:
                    - $ref: '#/components/schemas/CombinedMarketData'
                    - type: "null"
                  description: Latest market data or null if not found
                timestamp:
                  type: string
                  format: date-time
                  description: Response timestamp
              required:
                - currencyPair
                - data
                - timestamp

    HistoricalDataResponse:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                currencyPair:
                  type: string
                  description: Requested currency pair
                data:
                  type: array
                  items:
                    $ref: '#/components/schemas/CombinedMarketData'
                  description: Array of historical market data
                count:
                  type: integer
                  description: Number of records returned
                  minimum: 0
                dateRange:
                  type: object
                  properties:
                    start:
                      type: string
                      format: date-time
                      description: Start date of the range
                    end:
                      type: string
                      format: date-time
                      description: End date of the range
                  required:
                    - start
                    - end
              required:
                - currencyPair
                - data
                - count
                - dateRange

    AggregatedDataResponse:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                currencyPair:
                  type: string
                  description: Requested currency pair
                interval:
                  type: string
                  enum: [minute, hour, day, week, month]
                  description: Aggregation interval
                data:
                  type: array
                  items:
                    $ref: '#/components/schemas/AggregatedMarketData'
                  description: Array of aggregated market data
                count:
                  type: integer
                  description: Number of aggregated periods
                  minimum: 0
                dateRange:
                  type: object
                  properties:
                    start:
                      type: string
                      format: date-time
                    end:
                      type: string
                      format: date-time
                  required:
                    - start
                    - end
              required:
                - currencyPair
                - interval
                - data
                - count
                - dateRange

    RangeDataResponse:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                data:
                  type: array
                  items:
                    $ref: '#/components/schemas/CombinedMarketData'
                  description: Array of market data within the range
                count:
                  type: integer
                  description: Number of records returned
                  minimum: 0
                filters:
                  type: object
                  properties:
                    currencyPair:
                      type: string
                      description: Applied currency pair filter or "all"
                    dateRange:
                      type: object
                      properties:
                        start:
                          type: string
                          format: date-time
                        end:
                          type: string
                          format: date-time
                      required:
                        - start
                        - end
                    limit:
                      type: integer
                      description: Applied limit
                    sortOrder:
                      type: string
                      enum: [asc, desc]
                      description: Applied sort order
                  required:
                    - currencyPair
                    - dateRange
                    - sortOrder
              required:
                - data
                - count
                - filters

    MarketStatisticsResponse:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                currencyPair:
                  type: string
                  description: Currency pair
                period:
                  type: string
                  enum: [24h, 7d, 30d]
                  description: Statistics period
                totalRecords:
                  type: integer
                  description: Total number of data points analyzed
                  minimum: 0
                priceStats:
                  type: object
                  properties:
                    current:
                      type: number
                      description: Current price
                    min:
                      type: number
                      description: Minimum price in period
                    max:
                      type: number
                      description: Maximum price in period
                    average:
                      type: number
                      description: Average price in period
                    change:
                      type: number
                      description: Price change from start to end
                    changePercent:
                      type: number
                      description: Price change percentage
                  required:
                    - current
                    - min
                    - max
                    - average
                    - change
                    - changePercent
                volumeStats:
                  type: object
                  properties:
                    total:
                      type: number
                      description: Total volume in period
                    average:
                      type: number
                      description: Average volume per data point
                    min:
                      type: number
                      description: Minimum volume in period
                    max:
                      type: number
                      description: Maximum volume in period
                  required:
                    - total
                    - average
                    - min
                    - max
                dateRange:
                  type: object
                  properties:
                    start:
                      type: string
                      format: date-time
                    end:
                      type: string
                      format: date-time
                  required:
                    - start
                    - end
              required:
                - currencyPair
                - period
                - totalRecords
                - priceStats
                - volumeStats
                - dateRange

  responses:
    ValidationError:
      description: Request validation failed
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            success: false
            error: "Validation failed"
            message: "Invalid request parameters"
            details:
              - field: "currencyPair"
                message: "Invalid currency pair format"
                value: "invalid"
            timestamp: "2024-01-01T00:00:00.000Z"

    InternalServerError:
      description: Internal server error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            success: false
            error: "Internal server error"
            message: "An unexpected error occurred"
            timestamp: "2024-01-01T00:00:00.000Z"
